import process from "node:process"
import { Interval } from "./consts"
import { typeSafeObjectFromEntries } from "./type.util"
import type { OriginSource, Source, SourceID } from "./types"

const Time = {
  Test: 1,
  Realtime: 2 * 60 * 1000,
  Fast: 5 * 60 * 1000,
  Default: Interval, // 10min
  Common: 30 * 60 * 1000,
  Slow: 60 * 60 * 1000,
}

export const originSources = {
  "nmpa": {
    name: "NMPA",
    color: "blue",
    column: "medical",
    home: "https://www.nmpa.gov.cn/",
    desc: "国家药品监督管理局",
    interval: Time.Common,
    sub: {
      "device-registration": {
        title: "新器械注册（批准）",
        type: "realtime",
        home: "https://www.nmpa.gov.cn/datasearch/home-index.html",
        desc: "境内/进口医疗器械注册信息",
      },
      "device-recall": {
        title: "器械召回",
        type: "realtime",
        desc: "医疗器械召回信息监控",
      },
      "regulations": {
        title: "法规更新",
        type: "realtime",
        home: "https://www.nmpa.gov.cn/",
        desc: "政策法规和公告更新",
      },
    },
  },
  "fda": {
    name: "FDA",
    color: "red",
    column: "medical",
    home: "https://www.fda.gov/",
    desc: "美国食品药品监督管理局",
    interval: Time.Common,
    sub: {
      "510k": {
        title: "新器械许可 (510(k))",
        type: "realtime",
        home: "https://www.accessdata.fda.gov/scripts/cdrh/cfdocs/cfpmn/pmn.cfm",
        desc: "510(k) 上市前通知数据库",
      },
      "pma": {
        title: "新器械批准 (PMA)",
        type: "realtime",
        home: "https://www.accessdata.fda.gov/scripts/cdrh/cfdocs/cfpma/pma.cfm",
        desc: "上市前批准数据库，适用于高风险（III类）器械",
      },
      "device-recall": {
        title: "器械召回",
        type: "realtime",
        home: "https://www.accessdata.fda.gov/scripts/cdrh/cfdocs/cfres/res.cfm",
        desc: "医疗器械召回数据库",
      },
      "guidance": {
        title: "法规更新",
        type: "realtime",
        home: "https://www.fda.gov/regulatory-information/search-fda-guidance-documents",
        desc: "指导文件和法规更新",
      },
    },
  },

} as const satisfies Record<string, OriginSource>

export function genSources() {
  const _: [SourceID, Source][] = []

  Object.entries(originSources).forEach(([id, source]: [any, OriginSource]) => {
    const parent = {
      name: source.name,
      type: source.type,
      disable: source.disable,
      desc: source.desc,
      column: source.column,
      home: source.home,
      color: source.color ?? "primary",
      interval: source.interval ?? Time.Default,
    }
    if (source.sub && Object.keys(source.sub).length) {
      Object.entries(source.sub).forEach(([subId, subSource], i) => {
        if (i === 0) {
          _.push([
            id,
            {
              redirect: `${id}-${subId}`,
              ...parent,
              ...subSource,
            },
          ] as [any, Source])
        }
        _.push([`${id}-${subId}`, { ...parent, ...subSource }] as [
          any,
          Source,
        ])
      })
    } else {
      _.push([
        id,
        {
          title: source.title,
          ...parent,
        },
      ])
    }
  })

  return typeSafeObjectFromEntries(
    _.filter(([_, v]) => {
      if (v.disable === "cf" && process.env.CF_PAGES) {
        return false
      } else if (v.disable === true) {
        return false
      } else {
        return true
      }
    }),
  )
}
