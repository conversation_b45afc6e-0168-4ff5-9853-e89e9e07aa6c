# NewsNow

一个现代化的新闻聚合平台，支持多种新闻源的实时抓取和展示。

## 🚀 技术栈

### 前端技术
- **React 19** - 现代化的前端框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite 6** - 快速的构建工具
- **TanStack Router** - 类型安全的路由管理
- **TanStack Query** - 强大的数据获取和缓存库
- **Jotai** - 原子化状态管理
- **UnoCSS** - 原子化CSS框架
- **Framer Motion** - 动画库
- **PWA** - 渐进式Web应用支持

### 后端技术
- **Nitro** - 全栈Web框架
- **H3** - HTTP服务器框架
- **Better SQLite3** - 轻量级数据库
- **Cheerio** - 服务端HTML解析
- **Jose** - JWT处理
- **Ofetch** - HTTP客户端

### 开发工具
- **ESLint** - 代码质量检查
- **Vitest** - 单元测试框架
- **PNPM** - 包管理器
- **Docker** - 容器化部署
- **GitHub Actions** - CI/CD

## 📁 项目结构

```
newsnow-main/
├── .github/                    # GitHub Actions配置
│   └── workflows/
│       ├── auto-refresh.yml    # 自动刷新工作流
│       ├── docker.yml          # Docker构建
│       └── release.yml         # 发布流程
├── public/                     # 静态资源
│   ├── icons/                  # 新闻源图标
│   ├── robots.txt             # 搜索引擎爬虫配置
│   ├── sitemap.xml            # 网站地图
│   └── sw.js                  # Service Worker
├── scripts/                    # 构建脚本
│   ├── favicon.ts             # 图标生成脚本
│   ├── refresh.ts             # 刷新脚本
│   └── source.ts              # 新闻源生成脚本
├── server/                     # 后端代码
│   ├── api/                   # API路由
│   │   ├── enable-login.ts    # 登录启用检查
│   │   ├── latest.ts          # 最新版本API
│   │   ├── login.ts           # 登录API
│   │   ├── mcp.post.ts        # MCP协议支持
│   │   ├── me/                # 用户相关API
│   │   │   └── sync.ts        # 数据同步
│   │   ├── oauth/             # OAuth认证
│   │   │   └── github.ts      # GitHub OAuth
│   │   ├── proxy/             # 代理服务
│   │   └── s/                 # 新闻源API
│   │       └── entire.post.ts # 完整数据获取
│   ├── database/              # 数据库层
│   │   ├── cache.ts           # 缓存表
│   │   └── user.ts            # 用户表
│   ├── middleware/            # 中间件
│   │   └── auth.ts            # 认证中间件
│   ├── sources/               # 新闻源实现
│   │   ├── _36kr.ts           # 36氪
│   │   ├── baidu.ts           # 百度热搜
│   │   ├── bilibili.ts        # 哔哩哔哩
│   │   ├── github.ts          # GitHub趋势
│   │   ├── zhihu.ts           # 知乎热榜
│   │   └── ...                # 其他新闻源
│   └── utils/                 # 工具函数
│       ├── date.ts            # 日期处理
│       ├── fetch.ts           # 网络请求
│       ├── logger.ts          # 日志记录
│       └── source.ts          # 新闻源工具
├── shared/                     # 共享代码
│   ├── consts.ts              # 常量定义
│   ├── metadata.ts            # 元数据配置
│   ├── pre-sources.ts         # 新闻源预配置
│   ├── sources.json           # 新闻源配置文件
│   ├── types.ts               # 类型定义
│   └── utils.ts               # 共享工具函数
├── src/                        # 前端源码
│   ├── atoms/                 # Jotai状态原子
│   ├── components/            # React组件
│   │   ├── column/            # 栏目组件
│   │   ├── common/            # 通用组件
│   │   ├── footer.tsx         # 页脚组件
│   │   ├── header/            # 头部组件
│   │   └── navbar.tsx         # 导航栏组件
│   ├── hooks/                 # 自定义Hook
│   │   ├── query.ts           # 查询Hook
│   │   ├── useDark.ts         # 暗色模式
│   │   ├── useLogin.ts        # 登录Hook
│   │   ├── usePWA.ts          # PWA Hook
│   │   └── useSync.ts         # 同步Hook
│   ├── routes/                # 路由组件
│   │   ├── __root.tsx         # 根路由
│   │   ├── c.$column.tsx      # 栏目页面
│   │   └── index.tsx          # 首页
│   ├── styles/                # 样式文件
│   ├── utils/                 # 前端工具函数
│   └── main.tsx               # 应用入口
├── tools/                      # 开发工具
├── docker-compose.yml          # Docker Compose配置
├── Dockerfile                  # Docker镜像配置
├── nitro.config.ts            # Nitro配置
├── package.json               # 项目依赖
├── pwa.config.ts              # PWA配置
├── uno.config.ts              # UnoCSS配置
├── vite.config.ts             # Vite配置
└── vitest.config.ts           # 测试配置
```

## 🏗️ 架构设计

### 前端架构
- **组件化设计**: 使用React函数组件和Hook
- **状态管理**: Jotai原子化状态管理，支持本地存储持久化
- **路由管理**: TanStack Router提供类型安全的路由
- **数据获取**: TanStack Query处理服务端状态和缓存
- **样式系统**: UnoCSS原子化CSS，支持暗色模式
- **PWA支持**: 离线缓存、安装到桌面等功能

### 后端架构
- **全栈框架**: Nitro提供统一的前后端构建
- **API设计**: RESTful API，支持多种部署环境
- **数据库**: SQLite本地存储，支持Cloudflare D1
- **认证系统**: JWT + GitHub OAuth
- **缓存机制**: 内存缓存 + 数据库缓存
- **新闻源**: 模块化的新闻源实现，支持RSS和自定义爬虫

### 部署架构
- **多平台支持**: Node.js、Cloudflare Pages、Vercel Edge
- **容器化**: Docker支持，包含完整的运行环境
- **CI/CD**: GitHub Actions自动构建和部署

## 📋 核心功能

### 新闻聚合
- **多源支持**: 支持40+主流新闻源
- **实时更新**: 定时抓取最新内容
- **智能分类**: 自动分类到不同栏目
- **去重处理**: 避免重复内容展示

### 用户体验
- **响应式设计**: 适配桌面和移动端
- **暗色模式**: 支持系统主题切换
- **拖拽排序**: 自定义栏目和新闻源顺序
- **搜索功能**: 快速查找新闻内容
- **PWA支持**: 可安装到桌面，离线访问

### 数据同步
- **用户登录**: GitHub OAuth认证
- **云端同步**: 个人配置云端存储
- **跨设备**: 多设备数据同步

## 🚀 快速开始

### 环境要求
- Node.js 20+
- PNPM 10+

### 本地开发
```bash
# 克隆项目
git clone https://github.com/ourongxing/newsnow.git
cd newsnow

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 环境配置
复制 `example.env.server` 为 `.env.server` 并配置：
```env
G_CLIENT_ID=your_github_client_id
G_CLIENT_SECRET=your_github_client_secret
JWT_SECRET=your_jwt_secret
INIT_TABLE=true
ENABLE_CACHE=true
```

### Docker部署
```bash
# 使用Docker Compose
docker-compose up -d

# 或使用预构建镜像
docker run -d -p 4444:4444 \
  -e G_CLIENT_ID=your_client_id \
  -e G_CLIENT_SECRET=your_client_secret \
  -e JWT_SECRET=your_jwt_secret \
  ghcr.io/ourongxing/newsnow:latest
```

## 📝 开发指南

### 添加新闻源
1. 在 `server/sources/` 目录创建新文件
2. 实现 `defineSource` 函数
3. 在 `shared/pre-sources.ts` 中注册
4. 运行 `pnpm presource` 生成配置

### 自定义主题
1. 修改 `uno.config.ts` 中的颜色配置
2. 更新 `src/styles/` 中的样式文件

### 测试
```bash
# 运行单元测试
pnpm test

# 类型检查
pnpm typecheck

# 代码检查
pnpm lint
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的努力
- 感谢开源社区提供的优秀工具和库
- 感谢各大新闻平台提供的内容源

## 📞 联系方式

- 作者: [ourongxing](https://github.com/ourongxing)
- 邮箱: <EMAIL>
- 项目主页: https://github.com/ourongxing/newsnow
- 在线演示: https://newsnow.busiyi.world/